<template>
  <view class="user">
    <view>111</view>
    <tabbar />
  </view>
</template>

<script setup lang="ts">
import { getDecorate } from '@/api/home'
import { useUserStore } from '@/stores/user'
import { onShow } from '@dcloudio/uni-app'
import { storeToRefs } from 'pinia'
import { reactive } from 'vue'

const state = reactive<{
  pages: any[]
}>({
  pages: [],
})
const getData = async () => {
  const { data } = await getDecorate({ id: 2 })
  state.pages = JSON.parse(data.pageData)
}
const userStore = useUserStore()
const { userInfo, isLogin } = storeToRefs(userStore)
// onShow(() => {
//     userStore.getUser()
// })
// getData()
</script>

<style></style>
