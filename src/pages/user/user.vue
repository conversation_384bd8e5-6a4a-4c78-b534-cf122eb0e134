<template>
  <view class="user-page">

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 背景装饰图片 -->
      <image class="bg-decoration" src="/static/images/bg-decoration.png" mode="aspectFill" />

      <!-- 用户信息区域 -->
      <view class="user-info-section">
        <!-- 头像和基本信息 -->
        <view class="avatar-info">
          <view class="avatar-container">
            <view class="avatar-bg"></view>
            <image class="avatar-image" :src="userInfo.sysUser?.avatar || '/static/images/default-avatar.png'" mode="aspectFill" />
          </view>
          <view class="user-details">
            <text class="greeting">您好，{{ userInfo.sysUser?.nickname || '大鱼海棠' }}</text>
            <view class="org-breadcrumb">
              <text class="org-item">西南总部</text>
              <view class="arrow-icon"></view>
              <text class="org-item">四川采购部</text>
              <view class="arrow-icon"></view>
              <text class="org-item">爱养牛采购部</text>
            </view>
            <text class="phone">{{ userInfo.sysUser?.phone || '130****4278' }}</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单区域 -->
      <view class="menu-section">
        <!-- 消息中心和修改密码 -->
        <view class="menu-group">
          <view class="menu-item" @click="goToMessages">
            <view class="menu-content">
              <image class="menu-icon" src="/static/images/icon-notifications.svg" mode="aspectFit" />
              <text class="menu-title">消息中心</text>
              <view class="menu-right">
                <view class="badge">8</view>
                <image class="arrow-right" src="/static/images/icon-arrow-right.svg" mode="aspectFit" />
              </view>
            </view>
          </view>
          <view class="menu-item" @click="goToChangePassword">
            <view class="menu-content">
              <image class="menu-icon" src="/static/images/icon-lock.svg" mode="aspectFit" />
              <text class="menu-title">修改密码</text>
              <image class="arrow-right" src="/static/images/icon-arrow-right.svg" mode="aspectFit" />
            </view>
          </view>
        </view>

      </view>

      <!-- 退出登录 -->
      <view class="logout-section">
        <view class="menu-item logout-item" @click="handleLogout">
          <view class="menu-content">
            <image class="menu-icon" src="/static/images/icon-logout.svg" mode="aspectFit" />
            <text class="menu-title">退出登录</text>
            <image class="arrow-right" src="/static/images/icon-arrow-right.svg" mode="aspectFit" />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航 -->
    <tabbar />
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { onShow } from '@dcloudio/uni-app'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { userInfo, isLogin } = storeToRefs(userStore)

onShow(() => {
  // userStore.getUser()
})

// 跳转到消息中心
const goToMessages = () => {
  uni.navigateTo({
    url: '/pages/messages/messages'
  })
}

// 跳转到修改密码
const goToChangePassword = () => {
  uni.navigateTo({
    url: '/pages/change_password/change_password'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.user-page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #ECF2FF 0%, #F5F7FA 100%);
  position: relative;
  overflow: hidden;
}



/* 主要内容区域 */
.main-content {
  position: relative;
  flex: 1;

  .bg-decoration {
    position: absolute;
    top: -20px;
    left: -52px;
    width: 480px;
    height: 853px;
    opacity: 0.12;
    z-index: 0;
  }
}

/* 用户信息区域 */
.user-info-section {
  position: relative;
  z-index: 1;
  padding: 16px 12px 0;
  margin-top: 108px;

  .avatar-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar-container {
      position: relative;
      width: 56px;
      height: 56px;

      .avatar-bg {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 48px;
        height: 48px;
        background: #2A5AD8;
        border-radius: 132px;
      }

      .avatar-image {
        width: 56px;
        height: 56px;
        border-radius: 154px;
        border: 1px solid #FFFFFF;
        position: relative;
        z-index: 2;
      }
    }

    .user-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .greeting {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 18px;
        line-height: 1.4;
        color: #1C2026;
      }

      .org-breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;

        .org-item {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 13px;
          line-height: 1.4;
          color: #7E8694;
        }

        .arrow-icon {
          width: 0;
          height: 12px;
          border-right: 1px solid #CDD2DA;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%) rotate(45deg);
            width: 4px;
            height: 4px;
            border-right: 1px solid #CDD2DA;
            border-top: 1px solid #CDD2DA;
          }
        }
      }

      .phone {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        line-height: 1.4;
        color: #7E8694;
      }
    }
  }
}

/* 功能菜单区域 */
.menu-section {
  position: relative;
  z-index: 1;
  padding: 0 12px;
  margin-top: 40px;

  .menu-group {
    background: #FFFFFF;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 28px;

    .menu-item {
      padding-left: 16px;

      &:not(:last-child) {
        border-bottom: 0.5px solid #E4E7ED;
      }

      .menu-content {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 16px 16px 0;

        .menu-icon {
          width: 20px;
          height: 20px;
        }

        .menu-title {
          flex: 1;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          line-height: 1.5;
          color: #1C2026;
        }

        .menu-right {
          display: flex;
          align-items: center;
          gap: 8px;

          .badge {
            min-width: 8px;
            height: 16px;
            background: #F53F3F;
            border-radius: 999px;
            padding: 0 4px;
            display: flex;
            align-items: center;
            justify-content: center;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 10px;
            line-height: 1.6;
            color: #FFFFFF;
          }
        }

        .arrow-right {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .logout-section {
    background: #FFFFFF;
    border-radius: 12px;
    overflow: hidden;

    .logout-item {
      padding-left: 16px;

      .menu-content {
        border-bottom: 0.5px solid #E6EAF0;
      }
    }
  }
}
</style>
