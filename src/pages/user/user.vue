<template>
  <view class="user-page">
    <!-- 状态栏 -->
    <view class="status-bar">
      <view class="status-symbols">
        <view class="time">9:41</view>
        <view class="right-symbols">
          <view class="signal-bars">
            <view class="bar bar1"></view>
            <view class="bar bar2"></view>
            <view class="bar bar3"></view>
            <view class="bar bar4"></view>
          </view>
          <view class="wifi-icon">📶</view>
          <view class="battery">
            <view class="battery-level"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 小程序导航栏 -->
    <view class="navbar">
      <view class="navbar-capsule">
        <view class="more-btn">⋯</view>
        <view class="divider"></view>
        <view class="close-btn">✕</view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 背景装饰图片 -->
      <view class="bg-decoration"></view>

      <!-- 用户信息区域 -->
      <view class="user-info-section">
        <!-- 头像和基本信息 -->
        <view class="avatar-info">
          <view class="avatar-container">
            <view class="avatar-bg"></view>
            <image class="avatar-image" :src="userInfo.sysUser?.avatar || '/static/images/default-avatar.png'" mode="aspectFill" />
          </view>
          <view class="user-details">
            <text class="greeting">您好，{{ userInfo.sysUser?.nickname || '大鱼海棠' }}</text>
            <view class="org-breadcrumb">
              <text class="org-item">西南总部</text>
              <view class="arrow-icon"></view>
              <text class="org-item">四川采购部</text>
              <view class="arrow-icon"></view>
              <text class="org-item">爱养牛采购部</text>
            </view>
            <text class="phone">{{ userInfo.sysUser?.phone || '130****4278' }}</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单区域 -->
      <view class="menu-section">
        <!-- 消息中心和修改密码 -->
        <view class="menu-group">
          <view class="menu-item" @click="goToMessages">
            <view class="menu-content">
              <view class="menu-icon notifications-icon"></view>
              <text class="menu-title">消息中心</text>
              <view class="menu-right">
                <view class="badge">8</view>
                <view class="arrow-right"></view>
              </view>
            </view>
          </view>
          <view class="menu-item" @click="goToChangePassword">
            <view class="menu-content">
              <view class="menu-icon lock-icon"></view>
              <text class="menu-title">修改密码</text>
              <view class="arrow-right"></view>
            </view>
          </view>
        </view>

        <!-- 退出登录 -->
        <view class="logout-section">
          <view class="menu-item logout-item" @click="handleLogout">
            <view class="menu-content">
              <view class="menu-icon logout-icon"></view>
              <text class="menu-title">退出登录</text>
              <view class="arrow-right"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航 -->
    <tabbar />
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { onShow } from '@dcloudio/uni-app'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { userInfo, isLogin } = storeToRefs(userStore)

onShow(() => {
  // userStore.getUser()
})

// 跳转到消息中心
const goToMessages = () => {
  uni.navigateTo({
    url: '/pages/messages/messages'
  })
}

// 跳转到修改密码
const goToChangePassword = () => {
  uni.navigateTo({
    url: '/pages/change_password/change_password'
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.user-page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #ECF2FF 0%, #F5F7FA 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 46px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px 0 30px;

  .status-symbols {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .time {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
    }

    .right-symbols {
      display: flex;
      align-items: center;
      gap: 6px;

      .signal-bars {
        display: flex;
        gap: 2px;
        align-items: end;

        .bar {
          background: rgba(0, 0, 0, 0.9);
          border-radius: 1.2px;

          &.bar1 { width: 3px; height: 4px; }
          &.bar2 { width: 3px; height: 6px; }
          &.bar3 { width: 3px; height: 8.3px; }
          &.bar4 { width: 3px; height: 10.7px; }
        }
      }

      .wifi-icon {
        width: 15.4px;
        height: 11.06px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.9);
      }

      .battery {
        width: 24.5px;
        height: 11.5px;
        border: 1px solid rgba(0, 0, 0, 0.26);
        border-radius: 2px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          right: -2px;
          top: 50%;
          transform: translateY(-50%);
          width: 1.5px;
          height: 4px;
          background: rgba(0, 0, 0, 0.36);
          border-radius: 0 1px 1px 0;
        }

        .battery-level {
          width: 18px;
          height: 7.67px;
          background: rgba(0, 0, 0, 0.9);
          border-radius: 1.6px;
          margin: 1.92px 2px;
        }
      }
    }
  }
}

/* 小程序导航栏 */
.navbar {
  width: 100%;
  height: 48px;
  background: #FFFFFF;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 12px;

  .navbar-capsule {
    width: 87px;
    height: 32px;
    border: 0.5px solid #CDD2DA;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 13px;
    padding: 6px 13px;

    .more-btn, .close-btn {
      width: 18px;
      height: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #1C2026;
    }

    .divider {
      width: 0.5px;
      height: 20px;
      background: #CDD2DA;
    }
  }
}

/* 主要内容区域 */
.main-content {
  position: relative;
  flex: 1;

  .bg-decoration {
    position: absolute;
    top: -20px;
    left: -52px;
    width: 480px;
    height: 853px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgwIiBoZWlnaHQ9Ijg1MyIgdmlld0JveD0iMCAwIDQ4MCA4NTMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0ODAiIGhlaWdodD0iODUzIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMTcyN18xNTY5OCkiIGZpbGwtb3BhY2l0eT0iMC4xMiIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzE3MjdfMTU2OTgiIHgxPSIyNDAiIHkxPSIwIiB4Mj0iMjQwIiB5Mj0iODUzIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiNFQ0YyRkYiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRjVGN0ZBIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K');
    background-size: cover;
    background-position: center;
    opacity: 0.12;
    z-index: 0;
  }
}

/* 用户信息区域 */
.user-info-section {
  position: relative;
  z-index: 1;
  padding: 16px 12px 0;
  margin-top: 16px;

  .avatar-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar-container {
      position: relative;
      width: 56px;
      height: 56px;

      .avatar-bg {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 48px;
        height: 48px;
        background: #2A5AD8;
        border-radius: 132px;
      }

      .avatar-image {
        width: 56px;
        height: 56px;
        border-radius: 154px;
        border: 1px solid #FFFFFF;
        position: relative;
        z-index: 2;
      }
    }

    .user-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .greeting {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 18px;
        line-height: 1.4;
        color: #1C2026;
      }

      .org-breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;

        .org-item {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 13px;
          line-height: 1.4;
          color: #7E8694;
        }

        .arrow-icon {
          width: 0;
          height: 12px;
          border-right: 1px solid #CDD2DA;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%) rotate(45deg);
            width: 4px;
            height: 4px;
            border-right: 1px solid #CDD2DA;
            border-top: 1px solid #CDD2DA;
          }
        }
      }

      .phone {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        line-height: 1.4;
        color: #7E8694;
      }
    }
  }
}

/* 功能菜单区域 */
.menu-section {
  position: relative;
  z-index: 1;
  padding: 0 12px;
  margin-top: 40px;

  .menu-group {
    background: #FFFFFF;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 28px;

    .menu-item {
      padding-left: 16px;

      &:not(:last-child) {
        border-bottom: 0.5px solid #E4E7ED;
      }

      .menu-content {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 16px 16px 0;

        .menu-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          &.notifications-icon {
            &::before {
              content: '';
              position: absolute;
              width: 12.5px;
              height: 15.99px;
              top: 2.08px;
              left: 3.75px;
              background: url("data:image/svg+xml,%3Csvg width='13' height='16' viewBox='0 0 13 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.5 16C7.33 16 8 15.33 8 14.5H5C5 15.33 5.67 16 6.5 16ZM11 11.5V7.5C11 4.93 9.37 2.86 7 2.18V1.5C7 0.67 6.33 0 5.5 0C4.67 0 4 0.67 4 1.5V2.18C1.64 2.86 0 4.92 0 7.5V11.5L-2 13.5V14.5H13V13.5L11 11.5Z' fill='%231C1B1F'/%3E%3C/svg%3E") no-repeat center;
              background-size: contain;
            }
          }

          &.lock-icon {
            &::before {
              content: '';
              position: absolute;
              width: 12.5px;
              height: 16.25px;
              top: 1.67px;
              left: 3.75px;
              background: url("data:image/svg+xml,%3Csvg width='13' height='17' viewBox='0 0 13 17' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.8333 6.16667H10V4.33333C10 1.94167 8.05833 0 5.66667 0C3.275 0 1.33333 1.94167 1.33333 4.33333V6.16667H0.5C0.225 6.16667 0 6.39167 0 6.66667V15.5C0 15.775 0.225 16 0.5 16H10.8333C11.1083 16 11.3333 15.775 11.3333 15.5V6.66667C11.3333 6.39167 11.1083 6.16667 10.8333 6.16667ZM2.16667 4.33333C2.16667 2.40833 3.74167 0.833333 5.66667 0.833333C7.59167 0.833333 9.16667 2.40833 9.16667 4.33333V6.16667H2.16667V4.33333Z' fill='%231C1B1F'/%3E%3C/svg%3E") no-repeat center;
              background-size: contain;
            }
          }

          &.logout-icon {
            &::before {
              content: '';
              position: absolute;
              width: 14.17px;
              height: 14.17px;
              top: 2.92px;
              left: 2.92px;
              background: url("data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.83333 13.3333H2.5C2.04167 13.3333 1.66667 12.9583 1.66667 12.5V2.5C1.66667 2.04167 2.04167 1.66667 2.5 1.66667H5.83333V0H2.5C1.125 0 0 1.125 0 2.5V12.5C0 13.875 1.125 15 2.5 15H5.83333V13.3333ZM11.6667 10.8333L10.4917 9.65833L12.3167 7.83333H6.66667V6.16667H12.3167L10.4917 4.34167L11.6667 3.16667L15 6.5L11.6667 10.8333Z' fill='%231C1B1F'/%3E%3C/svg%3E") no-repeat center;
              background-size: contain;
            }
          }
        }

        .menu-title {
          flex: 1;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          line-height: 1.5;
          color: #1C2026;
        }

        .menu-right {
          display: flex;
          align-items: center;
          gap: 8px;

          .badge {
            min-width: 8px;
            height: 16px;
            background: #F53F3F;
            border-radius: 999px;
            padding: 0 4px;
            display: flex;
            align-items: center;
            justify-content: center;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 10px;
            line-height: 1.6;
            color: #FFFFFF;
          }
        }

        .arrow-right {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            width: 6.67px;
            height: 10.67px;
            top: 2.67px;
            left: 4.67px;
            background: url("data:image/svg+xml,%3Csvg width='7' height='11' viewBox='0 0 7 11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1.5 0.5L0.5 1.5L4 5L0.5 8.5L1.5 9.5L6 5L1.5 0.5Z' fill='%237E8694'/%3E%3C/svg%3E") no-repeat center;
            background-size: contain;
          }
        }
      }
    }
  }

  .logout-section {
    .logout-item {
      background: #FFFFFF;
      border-radius: 12px;
      padding-left: 16px;

      .menu-content {
        border-bottom: 0.5px solid #E6EAF0;
      }
    }
  }
}
</style>
