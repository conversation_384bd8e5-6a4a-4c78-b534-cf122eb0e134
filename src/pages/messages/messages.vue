<template>
  <view class="messages-page">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <image class="back-icon" src="/static/images/icon-arrow-left.svg" mode="aspectFit" />
        </view>
        <text class="nav-title">消息中心</text>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-container">
        <view class="search-box">
          <image class="search-icon" src="/static/images/icon-search.svg" mode="aspectFit" />
          <input class="search-input" placeholder="请输入关键词搜索" />
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="content">
      <view class="message-list">
        <!-- 未读消息 -->
        <view class="message-card" @click="openMessage(0)">
          <view class="message-header">
            <text class="message-title">这是消息的标题</text>
            <view class="unread-dot"></view>
          </view>
          <text class="message-content">从病虫害探测、土壤墒情监测智能系统，再到耕、种、管、收等智能机器人，近几年来，越来越多具有自主知识产权的新技术、智能产品开始出现在农业场景中。</text>
          <view class="message-footer">
            <text class="message-time">2025/02/12 12:22</text>
          </view>
        </view>

        <!-- 已读消息 -->
        <view class="message-card read" @click="openMessage(1)">
          <text class="message-title">这是消息的标题这是消息的标题这是消息的标题</text>
          <text class="message-content">从病虫害探测、土壤墒情监测智能系统，再到耕、种、管、收等智能机器人，近几年来，越来越多具有自主知识产权的新技术、智能产品开始出现在农业场景中。</text>
          <view class="message-footer">
            <text class="message-time">2025/02/12 12:22</text>
          </view>
        </view>

        <!-- 已读消息 -->
        <view class="message-card read" @click="openMessage(2)">
          <text class="message-title">这是消息的标题</text>
          <text class="message-content">从病虫害探测、土壤墒情监测智能系统，再到耕、种、管、收等智能机器人，近几年来，越来越多具有自主知识产权的新技术、智能产品开始出现在农业场景中。</text>
          <view class="message-footer">
            <text class="message-time">2025/02/12 12:22</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 打开消息详情
const openMessage = (index: number) => {
  console.log('打开消息', index)
  // 这里可以跳转到消息详情页面
  // uni.navigateTo({
  //   url: `/pages/message-detail/message-detail?id=${index}`
  // })
}
</script>

<style lang="scss" scoped>
.messages-page {
  width: 100%;
  min-height: 100vh;
  background: #F0F2F5;
  overflow: hidden;
}

/* 导航栏 */
.navbar {
  width: 100%;
  height: 48px;
  background: #FFFFFF;
  position: relative;
  z-index: 10;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 12px;

    .nav-left {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      .back-icon {
        width: 24px;
        height: 24px;
      }
    }

    .nav-title {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 18px;
      line-height: 1.44;
      color: #1C2026;
      text-align: center;
    }

    .nav-right {
      width: 24px;
      height: 24px;
    }
  }
}

/* 搜索区域 */
.search-section {
  width: 100%;
  background: #FFFFFF;
  padding: 8px 12px 12px;

  .search-container {
    width: 100%;

    .search-box {
      width: 100%;
      height: 44px;
      background: #F6F7F9;
      border-radius: 22px;
      display: flex;
      align-items: center;
      padding: 0 18px;
      position: relative;

      .search-icon {
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }

      .search-input {
        flex: 1;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.45);
        border: none;
        outline: none;
        background: transparent;
      }
    }
  }
}

/* 内容区域 */
.content {
  padding: 0 12px;
  margin-top: 0;

  .message-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

/* 消息卡片 */
.message-card {
  background: #FFFFFF;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;

  .message-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .message-title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      line-height: 1.5;
      color: #1D2129;
      flex: 1;
    }

    .unread-dot {
      width: 8px;
      height: 8px;
      background: #F53F3F;
      border-radius: 50%;
      margin-left: 4px;
    }
  }

  .message-title {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
    color: #4E5969;
    width: 100%;
    height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .message-content {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.57;
    color: #1D2129;
    width: 100%;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .message-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;

    .message-time {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.57;
      color: #7E8694;
      text-align: center;
    }
  }

  // 已读消息样式
  &.read {
    .message-title {
      color: #4E5969;
      font-weight: 600;
    }

    .message-content {
      color: #4E5969;
    }
  }
}
</style>
